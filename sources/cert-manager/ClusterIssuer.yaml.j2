apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: {{ ClusterIssuerName }}
spec:
  acme:
    email: {{ email }}
    server: https://acme-v02.api.letsencrypt.org/directory
    privateKeySecretRef:
      name: {{ ClusterIssuerName }}
    solvers:
    {% if acme_challenge == "dns01" %}
      - dns01:
          cloudflare:
            email: {{ cloudflare_email }}
            apiTokenSecretRef:
              name: {{ cloudflare_api_token }}
              key: api-token
    {% else %}
      - http01:
          ingress:
            class: {{ ingress_class }}
    {% endif %}
