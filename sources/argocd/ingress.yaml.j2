{% if ingress.enabled %}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: argocd-server-ingress
  namespace: {{ namespace }}
  annotations:
    {% if ingress.ingressClassName == "nginx" %}
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/backend-protocol: "HTTPS"
    {% elif ingress.ingressClassName == "traefik" %}
    traefik.ingress.kubernetes.io/router.tls: "true"
    traefik.ingress.kubernetes.io/router.entrypoints: "websecure"
    traefik.ingress.kubernetes.io/router.middlewares: "{{ namespace }}-redirect-https@kubernetescrd"
    {% endif %}
spec:
  ingressClassName: {{ ingress.ingressClassName }}
  rules:
  - host: {{ ingress.host }}
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: argocd-server
            port:
              number: 443
  {% if ingress.tls.enabled %}
  tls:
  - hosts:
    - {{ ingress.host }}
    secretName: {{ ingress.tls.secretName }}
  {% endif %}
{% endif %}
