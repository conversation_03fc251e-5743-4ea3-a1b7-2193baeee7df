global:
  domain: srv.nex.my.id

metallb:
  poolName: localIP-pool
  ipAddressPool: ************-************
  #interface: enp0s3

cert-manager:
  email: <EMAIL>
  ClusterIssuerName: letsencrypt-prod
  acme_challenge: dns01 #http01 or dns01
  cloudflare_email: <EMAIL>
  cloudflare_api_token: secret-token
  cloudflare_secret_name: cloudflare-api-token
  ingress_class: nginx

nginx-ingress:
  image: nginx

traefik-ingress:
  email: <EMAIL>
  acme_challenge: dns01 #http01 or dns01
  acme_email: <EMAIL>
  cloudflare_api_token: secret-token
  cloudflare_secret_name: cloudflare-api-token
  persistence_enabled: true
  storage_class: local-path

argocd:
  namespace: argocd
  version: v2.7.4
  ingress:
    enabled: true
    host: argocd.example.com
    ingressClassName: nginx  # Can be "nginx" or "traefik"
    tls:
      enabled: true
      secretName: argocd-tls
  admin:
    passwordSecret: argocd-admin-password

knative:
  namespace: knative-serving
  version: v1.10.1
  domain: knative.example.com
  network:
    ingress: kourier # or istio, contour
  autoscaler:
    minScale: 1
    maxScale: 5

wikijs:
  namespace: wikijs
  replicas: 1
  version: 2
  database:
    type: postgres
    host: postgres-db.database
    port: 5432
    user: wiki
    name: wiki
    secretName: wikijs-db-secret
  storage:
    className: local-path
    size: 1Gi
  resources:
    requests:
      memory: 128Mi
      cpu: 100m
    limits:
      memory: 512Mi
      cpu: 500m
  ingress:
    enabled: true
    host: wiki.example.com
    ingressClassName: nginx  # Can be "nginx" or "traefik"
    sslRedirect: true
    tls:
      enabled: true
      secretName: wikijs-tls

